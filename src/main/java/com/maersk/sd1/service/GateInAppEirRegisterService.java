package com.maersk.sd1.service;


import com.maersk.sd1.common.dto.AppEirConstants;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.dto.InspectionResult;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class GateInAppEirRegisterService {

    private static final Logger logger = LogManager.getLogger(GateInAppEirRegisterService.class);

    private final EirRepository eirRepository;
    private final EirSendAppeirRepository eirSendAppeirRepository;
    private final DepotCredentialAppeirRepository depotCredentialAppeirRepository;
    private final EirActivityZoneRepository eirActivityZoneRepository;
    private final EstimateEmrRepository estimateEmrRepository;
    private final CatalogRepository catalogRepository;
    private final ContainerRepository containerRepository;

    @Transactional
    public void processGateInAppEirRegister(Integer eirId, String comment) {
        logger.info("Processing Gate In AppEIR registration for EIR ID: {}", eirId);

        try {
            Integer gateInMovementId = getCatalogIdByAlias();

            Optional<Eir> eirOpt = eirRepository.findByIdWithDetails(eirId);
            if (eirOpt.isEmpty()) {
                logger.warn("EIR not found with ID: {}", eirId);
                return;
            }

            Eir eir = eirOpt.get();

            if (Boolean.FALSE.equals(eir.getActive())) {
                handleInactiveEir(eirId);
                return;
            }

            if (!gateInMovementId.equals(eir.getCatMovement().getId())) {
                logger.debug("EIR {} is not a Gate In movement, skipping", eirId);
                return;
            }

            processGateInLogic(eir, comment);

        } catch (Exception e) {
            logger.error("Error processing Gate In AppEIR registration for EIR ID: {}", eirId, e);
            throw e;
        }
    }


    private void handleInactiveEir(Integer eirId) {
        logger.info("Handling inactive EIR: {}", eirId);

        if (!eirSendAppeirRepository.findByEirIdAndActive(eirId, true).isEmpty()) {
            eirSendAppeirRepository.deactivateByEirIdWithComment(
                eirId,
                "eir deleted",
                AppEirConstants.TRACE_DESACTIVE1
            );
            logger.info("Deactivated EirSendAppeir records for inactive EIR: {}", eirId);
        }
    }

    private void processGateInLogic(Eir eir, String comment) {
        Integer eirId = eir.getId();
        Integer subBusinessUnitId = eir.getSubBusinessUnit().getId();
        Integer shippingLineId = eir.getShippingLine().getId();

        logger.debug("Processing Gate In logic for EIR: {}, SubBusinessUnit: {}, ShippingLine: {}",
                    eirId, subBusinessUnitId, shippingLineId);

        Optional<DepotCredentialAppeir> depotCredentialOpt = depotCredentialAppeirRepository
                .findBySubBusinessUnitAndShippingLineForGateIn(subBusinessUnitId, shippingLineId);

        if (depotCredentialOpt.isEmpty()) {
            handleMissingDepotCredential(eirId, shippingLineId);
            return;
        }

        DepotCredentialAppeir depotCredential = depotCredentialOpt.get();
        processInspectionLogic(eir, depotCredential, comment);
    }


    private void handleMissingDepotCredential(Integer eirId, Integer shippingLineId) {
        logger.warn("Depot credential not configured for EIR: {}, ShippingLine: {}", eirId, shippingLineId);

        if (!eirSendAppeirRepository.findByEirIdAndActive(eirId, true).isEmpty()) {
            String comment = "shipping line not configured for transmissions";
            eirSendAppeirRepository.deactivateByEirIdWithComment(
                eirId,
                comment,
                AppEirConstants.TRACE_DESACTIVE2
            );
        }
    }

    private void processInspectionLogic(Eir eir, DepotCredentialAppeir depotCredential, String comment) {
        Integer eirId = eir.getId();
        Integer containerId = eir.getContainer().getId();

        if (!isWithinProcessingTimeLimit(eir.getTruckArrivalDate())) {
            logger.debug("EIR {} is outside processing time limit, skipping", eirId);
            return;
        }

        boolean isReefer = isReeferContainer(containerId);

        InspectionResult inspectionResult = getInspectionResults(eirId, isReefer);

        // Find or create EirSendAppeir record
        Optional<EirSendAppeir> existingRecordOpt = eirSendAppeirRepository.findActiveNotSentByEirId(eirId);

        if (existingRecordOpt.isEmpty()) {
            // Create new record if none exists
            createNewEirSendAppeirRecord(eirId, depotCredential, inspectionResult);
        } else {
            // Update existing record
            updateExistingEirSendAppeirRecord(existingRecordOpt.get(), inspectionResult);
        }

        // Handle additional record creation for damage cases
        handleAdditionalRecordCreation(eir, depotCredential, inspectionResult, comment);
    }

    /**
     * Check if EIR is within processing time limit
     */
    private boolean isWithinProcessingTimeLimit(LocalDateTime truckArrivalDate) {
        if (truckArrivalDate == null) {
            return false;
        }

        long daysDifference = ChronoUnit.DAYS.between(truckArrivalDate, LocalDateTime.now());
        return daysDifference <= AppEirConstants.DAYS_LIMIT_FOR_PROCESSING;
    }


    private boolean isReeferContainer(Integer containerId) {
        Optional<Container> containerOpt = containerRepository.findByIdWithType(containerId);
        if (containerOpt.isEmpty()) {
            return false;
        }

        Container container = containerOpt.get();
        String containerTypeCode = container.getCatContainerType().getCode();

        return containerTypeCode != null && !AppEirConstants.CONTAINER_TYPE_DRY_CODE.equals(containerTypeCode);
    }

    /**
     * Get inspection results for structure and machinery
     */
    private InspectionResult getInspectionResults(Integer eirId, boolean isReefer) {
        InspectionResult.InspectionResultBuilder builder = InspectionResult.builder()
                .structureInspectionCompleted(false)
                .structureDamaged(false)
                .machineryInspectionCompleted(false)
                .machineryDamaged(false)
                .scopeInspection(AppEirConstants.SCOPE_INSPECTION_NONE)
                .isDamagedContainer(false);

        Optional<EirActivityZone> structureActivityOpt = eirActivityZoneRepository
                .findByEirIdAndActivityAlias(eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_STRUCTURE_ALIAS);

        if (structureActivityOpt.isPresent()) {
            EirActivityZone structureActivity = structureActivityOpt.get();
            if (Boolean.TRUE.equals(structureActivity.getConcluded())) {
                builder.structureInspectionCompleted(true)
                       .scopeInspection(AppEirConstants.SCOPE_INSPECTION_STRUCTURE_ONLY);

                if (Boolean.TRUE.equals(structureActivity.getStructureDamagedResult())) {
                    builder.structureDamaged(true)
                           .isDamagedContainer(true);

                    Optional<EstimateEmr> structureEstimateOpt = estimateEmrRepository
                            .findLatestByEirIdAndEstimateTypeAlias(eirId, AppEirConstants.CATALOG_ESTIMATE_TYPE_STRUCTURE_ALIAS);
                    structureEstimateOpt.ifPresent(estimate -> builder.estimatedStructureId(estimate.getId()));
                }
            }
        }

        if (isReefer) {
            Optional<EirActivityZone> machineryActivityOpt = eirActivityZoneRepository
                    .findByEirIdAndActivityAlias(eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_MACHINERY_ALIAS);

            if (machineryActivityOpt.isPresent()) {
                EirActivityZone machineryActivity = machineryActivityOpt.get();
                if (Boolean.TRUE.equals(machineryActivity.getConcluded()) && !eirSendAppeirRepository.existsSentWithScopeInspection(eirId, AppEirConstants.SCOPE_INSPECTION_MACHINERY_ONLY)) {
                        boolean controlFlag = eirSendAppeirRepository.existsSentWithScopeInspection(eirId, AppEirConstants.SCOPE_INSPECTION_STRUCTURE_ONLY);

                        if (controlFlag) {
                            builder.estimatedStructureId(null)
                                   .isDamagedContainer(false);
                        }

                        String currentScope = builder.build().getScopeInspection();
                        String newScope = AppEirConstants.SCOPE_INSPECTION_STRUCTURE_ONLY.equals(currentScope) && !controlFlag
                                ? AppEirConstants.SCOPE_INSPECTION_BOTH
                                : AppEirConstants.SCOPE_INSPECTION_MACHINERY_ONLY;

                        builder.machineryInspectionCompleted(true)
                               .scopeInspection(newScope);

                        if (Boolean.TRUE.equals(machineryActivity.getMachineryDamagedResult())) {
                            builder.machineryDamaged(true)
                                   .isDamagedContainer(true);

                            Optional<EstimateEmr> machineryEstimateOpt = estimateEmrRepository
                                    .findLatestByEirIdAndEstimateTypeAlias(eirId, AppEirConstants.CATALOG_ESTIMATE_TYPE_MACHINERY_ALIAS);
                            machineryEstimateOpt.ifPresent(estimate -> builder.estimatedMachineryId(estimate.getId()));
                        }
                    }

            }
        }

        return builder.build();
    }

    /**
     * Create new EirSendAppeir record
     */
    private void createNewEirSendAppeirRecord(Integer eirId, DepotCredentialAppeir depotCredential, InspectionResult inspectionResult) {
        // Check if record already exists
        if (eirSendAppeirRepository.findByEirIdAndActive(eirId, true).isEmpty()) {
            EirSendAppeir newRecord = EirSendAppeir.builder()
                    .eir(new Eir(eirId))
                    .depotCredentialAppeirId(depotCredential.getId())
                    .subBusinessUnitId(depotCredential.getSubBusinessUnit().getId())
                    .flagSend(AppEirConstants.FLAG_SEND_NOT_SENT)
                    .registrationDate(LocalDateTime.now())
                    .active(true)
                    .comment(AppEirConstants.COMMENT_ADAPTATION)
                    .isNewInsert(false)
                    .appeirTrace(AppEirConstants.TRACE_NEW2)
                    .build();

            eirSendAppeirRepository.save(newRecord);
            logger.info("Created new EirSendAppeir record for EIR: {}", eirId);
        }
    }

    /**
     * Update existing EirSendAppeir record with inspection details
     */
    private void updateExistingEirSendAppeirRecord(EirSendAppeir existingRecord, InspectionResult inspectionResult) {
        eirSendAppeirRepository.updateInspectionDetails(
                existingRecord.getId(),
                inspectionResult.getScopeInspection(),
                inspectionResult.getIsDamagedContainer(),
                inspectionResult.getEstimatedStructureId(),
                inspectionResult.getEstimatedMachineryId()
        );

        logger.info("Updated EirSendAppeir record: {} with inspection details", existingRecord.getId());
    }

    /**
     * Handle additional record creation for damage cases
     */
    private void handleAdditionalRecordCreation(Eir eir, DepotCredentialAppeir depotCredential,
                                               InspectionResult inspectionResult, String comment) {
        Integer eirId = eir.getId();

        // Check conditions for additional record creation
        if (eir.getTruckDepartureDate() != null &&
            eirSendAppeirRepository.existsSentAndActiveByEirId(eirId) &&
            Boolean.TRUE.equals(inspectionResult.getIsDamagedContainer()) &&
            isWithinProcessingTimeLimit(eir.getTruckArrivalDate()) &&
            !eirSendAppeirRepository.existsWithSameCriteria(
                    eirId,
                    inspectionResult.getScopeInspection(),
                    inspectionResult.getEstimatedStructureId(),
                    inspectionResult.getEstimatedMachineryId()
            )) {

            String recordComment = AppEirConstants.COMMENT_TRIGGER.equals(comment)
                    ? AppEirConstants.COMMENT_ADD1
                    : AppEirConstants.COMMENT_ADD2;

            EirSendAppeir additionalRecord = EirSendAppeir.builder()
                    .eir(new Eir(eirId))
                    .depotCredentialAppeirId(depotCredential.getId())
                    .subBusinessUnitId(depotCredential.getSubBusinessUnit().getId())
                    .flagSend(AppEirConstants.FLAG_SEND_NOT_SENT)
                    .registrationDate(LocalDateTime.now())
                    .active(true)
                    .comment(recordComment)
                    .scopeInspection(inspectionResult.getScopeInspection())
                    .isDamagedContainer(inspectionResult.getIsDamagedContainer())
                    .estimatedEstructureId(inspectionResult.getEstimatedStructureId())
                    .estimatedMachineryId(inspectionResult.getEstimatedMachineryId())
                    .isNewInsert(true)
                    .appeirTrace(AppEirConstants.TRACE_NEW3)
                    .build();

            eirSendAppeirRepository.save(additionalRecord);
            logger.info("Created additional EirSendAppeir record for damaged container EIR: {}", eirId);
        }
    }

    private Integer getCatalogIdByAlias() {
        return catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS)
                .orElseThrow(() -> new IllegalStateException("Catalog not found for alias: " + AppEirConstants.CATALOG_GATE_IN_ALIAS));
    }
}
