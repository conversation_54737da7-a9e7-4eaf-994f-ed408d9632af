package com.maersk.sd1.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.dto.GetPendingTransmissionAppeirOutput;
import com.maersk.sd1.dto.GetPendingTransmissionAppeirOutput.*;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


@Service
@RequiredArgsConstructor
public class GetPendingTransmissionAppeirService {

    private static final Logger logger = LogManager.getLogger(GetPendingTransmissionAppeirService.class);
    private static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String DEFAULT_PHOTO_URL = "*************************************************************************************?sp=r&st=2024-03-22T21:54:58Z&se=2031-03-23T05:54:58Z&spr=https&sv=2022-11-02&sr=b&sig=Jgb4AuLlkFgH8QHRoRE8b7xeackBCcDphnVq9O9Xd3A%3D";

    private final EirSendAppeirRepository eirSendAppeirRepository;
    private final DepotCredentialAppeirRepository depotCredentialRepository;
    private final CatalogRepository catalogRepository;
    private final EirRepository eirRepository;
    private final EstimateEmrRepository estimateEmrRepository;
    private final EstimateEmrDetailRepository estimateEmrDetailRepository;
    private final EstimateEmrDetailPhotoRepository estimateEmrDetailPhotoRepository;
    private final AttachmentRepository attachmentRepository;

    @Transactional
    public GetPendingTransmissionAppeirOutput getPendingTransmissions(Integer depotCredentialAppeirId) {
        try {
            logger.info("Processing pending appEIR transmissions for depot credential ID: {}",
                    depotCredentialAppeirId);

            DepotCredentialAppeir credentials = getDepotCredentials(depotCredentialAppeirId);

            if (Boolean.FALSE.equals(credentials.getSendGateIn())) {
                logger.info("Gate-in transmission is disabled for depot credential ID: {}",
                        depotCredentialAppeirId);
                return new GetPendingTransmissionAppeirOutput();
            }

//            Integer gateOutId = catalogRepository.findIdByAlias("43081");
//            Integer gateInId = catalogRepository.findIdByAlias("43080");

            List<EirSendAppeir> pendingTransmissions = getPendingEirSendAppeir(credentials);

            updateEirSendAppeirData(pendingTransmissions);

            GetPendingTransmissionAppeirOutput output = generateOutputData(pendingTransmissions, credentials);

            updateActivityDates(pendingTransmissions);

            logger.info("Successfully processed {} pending transmissions", pendingTransmissions.size());
            return output;

        } catch (Exception ex) {
            logger.error("Error processing pending appEIR transmissions", ex);
            throw new RuntimeException("Failed to process pending appEIR transmissions", ex);
        }
    }

    private DepotCredentialAppeir getDepotCredentials(Integer depotCredentialAppeirId) {
        return depotCredentialRepository.findById(depotCredentialAppeirId)
                .orElseThrow(() -> new RuntimeException("Depot credential not found: " + depotCredentialAppeirId));
    }

    private List<EirSendAppeir> getPendingEirSendAppeir(DepotCredentialAppeir credentials) {
        List<EirSendAppeir> allPending = new ArrayList<>();

        allPending.addAll(eirSendAppeirRepository.findPendingWithoutTransmission(
                credentials.getSubBusinessUnit().getId(),
                credentials.getShippingLineId()
        ));

        allPending.addAll(eirSendAppeirRepository.findPendingWithTransmission(
                credentials.getSubBusinessUnit().getId(),
                credentials.getShippingLineId()
        ));

        return allPending;
    }

    private void updateEirSendAppeirData(List<EirSendAppeir> pendingTransmissions) {
        for (EirSendAppeir eirSendAppeir : pendingTransmissions) {

            updateEirSendAppeirFields(eirSendAppeir);
        }
    }

    private void updateEirSendAppeirFields(EirSendAppeir eirSendAppeir) {
        Eir eir = eirSendAppeir.getEir();

        String scopeInspection = determineScopeInspection(eir);
        eirSendAppeir.setScopeInspection(scopeInspection);

        Boolean isDamaged = eir.getStructureDamaged() || eir.getMachineryDamaged();
        eirSendAppeir.setIsDamagedContainer(isDamaged);

        if (isDamaged) {
            List<EstimateEmr> estimates = estimateEmrRepository.findByEirId(eir.getId());
            for (EstimateEmr estimate : estimates) {
                if (estimate.getIsStructureScope()) {
                    eirSendAppeir.setEstimatedEstructureId(estimate.getId());
                }
                if (estimate.getIsMachineryScope()) {
                    eirSendAppeir.setEstimatedMachineryId(estimate.getId());
                }
            }
        }

        repository.save(eirSendAppeir);
    }

    private String determineScopeInspection(Eir eir) {
        // Logic to determine scope based on container type and inspection results
        if (eir.getStructureDamaged() && eir.getMachineryDamaged()) {
            return "B"; // Both
        } else if (eir.getStructureDamaged()) {
            return "S"; // Structure
        } else if (eir.getMachineryDamaged()) {
            return "M"; // Machinery
        }
        return ""; // No damage
    }

    private GetPendingTransmissionAppeirOutput generateOutputData(List<EirSendAppeir> pendingTransmissions,
                                                                  DepotCredentialAppeir credentials) {
        GetPendingTransmissionAppeirOutput output = new GetPendingTransmissionAppeirOutput();

        List<AppeirInspectionHeader> headers = new ArrayList<>();
        List<AppeirInspectionDetail> details = new ArrayList<>();
        List<AppeirInspectionPicture> pictures = new ArrayList<>();

        for (EirSendAppeir eirSendAppeir : pendingTransmissions) {
            if (eirSendAppeir.getIsDamagedContainer()) {
                // Process damaged containers
                processDamagedContainer(eirSendAppeir, credentials, headers, details, pictures);
            } else {
                // Process non-damaged containers
                processNonDamagedContainer(eirSendAppeir, credentials, headers, details, pictures);
            }
        }

        output.setInspectionHeaders(headers);
        output.setInspectionDetails(details);
        output.setInspectionPictures(pictures);

        return output;
    }

    private void processDamagedContainer(EirSendAppeir eirSendAppeir, DepotCredentialAppeir credentials,
                                         List<AppeirInspectionHeader> headers, List<AppeirInspectionDetail> details,
                                         List<AppeirInspectionPicture> pictures) {

        // Process structure damage if exists
        if (eirSendAppeir.getEstimatedEstructureId() != null) {
            processEstimateEmr(eirSendAppeir, eirSendAppeir.getEstimatedEstructureId(), credentials,
                    headers, details, pictures);
        }

        // Process machinery damage if exists
        if (eirSendAppeir.getEstimatedMachineryId() != null) {
            processEstimateEmr(eirSendAppeir, eirSendAppeir.getEstimatedMachineryId(), credentials,
                    headers, details, pictures);
        }
    }

    private void processEstimateEmr(EirSendAppeir eirSendAppeir, Integer estimateEmrId,
                                    DepotCredentialAppeir credentials, List<AppeirInspectionHeader> headers,
                                    List<AppeirInspectionDetail> details, List<AppeirInspectionPicture> pictures) {

        Optional<EstimateEmr> estimateOpt = estimateEmrRepository.findById(estimateEmrId);
        if (estimateOpt.isEmpty()) {
            return;
        }

        EstimateEmr estimate = estimateOpt.get();
        Eir eir = eirSendAppeir.getEir();

        // Create header
        AppeirInspectionHeader header = createInspectionHeader(eirSendAppeir, estimate, credentials);
        headers.add(header);

        // Create details for damages
        List<EstimateEmrDetail> damageDetails = estimateEmrDetailRepository.findByEstimateEmrIdAndCustomerOrShippingLineResponsible(estimateEmrId);
        for (EstimateEmrDetail damageDetail : damageDetails) {
            AppeirInspectionDetail detail = createInspectionDetail(damageDetail, header.getInspectionId());
            details.add(detail);

            // Create pictures for this damage
            List<AppeirInspectionPicture> damagePhotos = createInspectionPictures(damageDetail.getId());
            pictures.addAll(damagePhotos);
        }
    }

    private void processNonDamagedContainer(EirSendAppeir eirSendAppeir, DepotCredentialAppeir credentials,
                                            List<AppeirInspectionHeader> headers, List<AppeirInspectionDetail> details,
                                            List<AppeirInspectionPicture> pictures) {

        // Create header for non-damaged container
        AppeirInspectionHeader header = createNonDamagedInspectionHeader(eirSendAppeir, credentials);
        headers.add(header);

        // Create dummy detail
        AppeirInspectionDetail detail = createDummyInspectionDetail(header.getInspectionId(), eirSendAppeir.getEir().getId());
        details.add(detail);

        // Create dummy picture
        AppeirInspectionPicture picture = createDummyInspectionPicture(detail.getInspectionDetailId());
        pictures.add(picture);
    }

    private AppeirInspectionHeader createInspectionHeader(EirSendAppeir eirSendAppeir, EstimateEmr estimate,
                                                          DepotCredentialAppeir credentials) {
        AppeirInspectionHeader header = new AppeirInspectionHeader();
        Eir eir = eirSendAppeir.getEir();
        Container container = eir.getContainer();

        header.setCompany(estimate.getSubBusinessUnit().getId());
        header.setInspectionId(eirSendAppeir.getId());
        header.setCreatedLocalDate(formatDateTime(eirSendAppeir.getActivityDate()));
        header.setSubmittedLocalDate(formatDateTime(eirSendAppeir.getActivityDate()));

        // Build comments
        StringBuilder comments = new StringBuilder("Inspection #").append(estimate.getId());
        if (estimate.getInspectorPerson() != null) {
            Person inspector = estimate.getInspectorPerson();
            comments.append(" generated by ")
                    .append(inspector.getNames()).append(" ")
                    .append(inspector.getFirstLastName()).append(" ")
                    .append(inspector.getSecondLastName() != null ? inspector.getSecondLastName() : "");
        }
        comments.append(" in ").append(credentials.getSubBusinessUnit().getName());
        header.setComments(comments.toString());

        header.setUserid(credentials.getClientId());
        header.setContainerStatus(1); // Empty
        header.setOperator(1); // Maersk
        header.setMoveType(1); // POS
        header.setMoveStatus(1); // IN
        header.setBookingNo("");
        header.setContainerNo(container.getContainerNumber());
        header.setEmail(credentials.getShopEmailCopy() != null ? credentials.getShopEmailCopy() : "");
        header.setCategoryEmail("Others");

        // Driver information
        if (eir.getDriverPerson() != null) {
            Person driver = eir.getDriverPerson();
            header.setTruckerRegno(driver.getIdentificationDocument().substring(0, Math.min(20, driver.getIdentificationDocument().length())));
            String driverName = (driver.getNames() != null ? driver.getNames() : "") + " " +
                    (driver.getFirstLastName() != null ? driver.getFirstLastName() : "") + " " +
                    (driver.getSecondLastName() != null ? driver.getSecondLastName() : "");
            header.setTruckerName(driverName.substring(0, Math.min(300, driverName.length())));
        }

        // Container information
        header.setContainerType(container.getCatContainerType().getDescription());
        header.setContainerClass(container.getCatGrade() != null ? container.getCatGrade().getDescription() : "");
        header.setContainerSize(container.getCatSize().getDescription());
        header.setEquipmentISOCode(container.getIsoCode() != null ? container.getIsoCode().getIsoCode() : "");
        header.setEquipmentSubType(determineEquipmentSubType(container.getCatFamily().getDescription()));

        header.setEstimateNumber(estimate.getId());
        header.setEirId(eir.getId());
        header.setScopeInspection(eirSendAppeir.getScopeInspection());
        header.setIsDamagedContainer(eirSendAppeir.getIsDamagedContainer());
        header.setInspectionDate(formatDateTime(eir.getDateRevision()));
        header.setTruckArrival(formatDateTime(eir.getTruckArrivalDate()));
        header.setTruckDeparture(formatDateTime(eir.getTruckDepartureDate()));
        header.setActivityDate(formatDateTime(eirSendAppeir.getActivityDate()));
        header.setIsNewInsert(eirSendAppeir.getIsNewInsert());

        return header;
    }

    private AppeirInspectionHeader createNonDamagedInspectionHeader(EirSendAppeir eirSendAppeir,
                                                                    DepotCredentialAppeir credentials) {
        AppeirInspectionHeader header = new AppeirInspectionHeader();
        Eir eir = eirSendAppeir.getEir();
        Container container = eir.getContainer();

        header.setCompany(eirSendAppeir.getSubBusinessUnitId());
        header.setInspectionId(eirSendAppeir.getId());
        header.setCreatedLocalDate(formatDateTime(eirSendAppeir.getActivityDate()));
        header.setSubmittedLocalDate(formatDateTime(eirSendAppeir.getActivityDate()));

        StringBuilder comments = new StringBuilder();
        if (eir.getInspectorPerson() != null) {
            Person inspector = eir.getInspectorPerson();
            comments.append("Inspection generated by ")
                    .append(inspector.getNames()).append(" ")
                    .append(inspector.getFirstLastName()).append(" ")
                    .append(inspector.getSecondLastName() != null ? inspector.getSecondLastName() : "")
                    .append(" in ");
        }
        comments.append(credentials.getSubBusinessUnit().getName());
        header.setComments(comments.toString());

        header.setUserid(credentials.getClientId());
        header.setContainerStatus(1);
        header.setOperator(1);
        header.setMoveType(1);
        header.setMoveStatus(1);
        header.setBookingNo("");
        header.setContainerNo(container.getContainerNumber());
        header.setEmail(credentials.getShopEmailCopy() != null ? credentials.getShopEmailCopy() : "");
        header.setCategoryEmail("Others");

        // Driver information
        if (eir.getDriverPerson() != null) {
            Person driver = eir.getDriverPerson();
            header.setTruckerRegno(driver.getIdentificationDocument().substring(0, Math.min(20, driver.getIdentificationDocument().length())));
            String driverName = (driver.getNames() != null ? driver.getNames() : "") + " " +
                    (driver.getFirstLastName() != null ? driver.getFirstLastName() : "") + " " +
                    (driver.getSecondLastName() != null ? driver.getSecondLastName() : "");
            header.setTruckerName(driverName.substring(0, Math.min(300, driverName.length())));
        }

        // Container information
        header.setContainerType(container.getCatContainerType().getDescription());
        header.setContainerClass(container.getCatGrade() != null ? container.getCatGrade().getDescription() : "");
        header.setContainerSize(container.getCatSize().getDescription());
        header.setEquipmentISOCode(container.getIsoCode() != null ? container.getIsoCode().getIsoCode() : "");
        header.setEquipmentSubType(determineEquipmentSubType(container.getCatFamily().getDescription()));

        header.setEstimateNumber(0);
        header.setEirId(eir.getId());
        header.setScopeInspection(eirSendAppeir.getScopeInspection());
        header.setIsDamagedContainer(eirSendAppeir.getIsDamagedContainer());
        header.setInspectionDate(formatDateTime(eir.getDateRevision()));
        header.setTruckArrival(formatDateTime(eir.getTruckArrivalDate()));
        header.setTruckDeparture(formatDateTime(eir.getTruckDepartureDate()));
        header.setActivityDate(formatDateTime(eirSendAppeir.getActivityDate()));
        header.setIsNewInsert(eirSendAppeir.getIsNewInsert());

        return header;
    }

    private AppeirInspectionDetail createInspectionDetail(EstimateEmrDetail damageDetail, Integer inspectionId) {
        AppeirInspectionDetail detail = new AppeirInspectionDetail();

        // Get catalog descriptions
        String responsibleCode = damageDetail.getCatAssumeCostEstimate().getCode();
        boolean isCustomerOrShippingLine = "C".equals(responsibleCode) || "L".equals(responsibleCode);

        if (isCustomerOrShippingLine) {
            String damageTypeCode = damageDetail.getCatDamageTypeEstimate().getCode();
            if ("8".equals(damageTypeCode)) {
                detail.setDamageLocation("0000");
                detail.setTpIndicator(3); // Wear and Tear
            } else {
                detail.setDamageLocation(damageDetail.getCatDamageLocationEstimate().getDescription());
                detail.setTpIndicator(2); // Third Party
            }
            detail.setPanelIndicatorCode(damageDetail.getCatDamageLocationEstimate().getDescription().substring(0, 1));
            detail.setPanelImageFilename("D_sketch_DX24_1");
            detail.setDamageCarrierCode(4); // Unknown Party
        } else {
            detail.setDamageLocation("0000");
            detail.setTpIndicator(0);
            detail.setPanelIndicatorCode("");
            detail.setPanelImageFilename("");
            detail.setDamageCarrierCode(0);
        }

        detail.setDamageType(damageDetail.getCatDamageTypeEstimate().getCode());
        detail.setDescription(damageDetail.getCatDamageTypeEstimate().getDescription());
        detail.setIsDamage(true);
        detail.setMissingSpare("6".equals(damageDetail.getCatDamageTypeEstimate().getCode()) ?
                Integer.valueOf(damageDetail.getCatDamageTypeEstimate().getCode()) : 0);
        detail.setInspectionDetailId(damageDetail.getId());
        detail.setInspectionId(inspectionId);
        detail.setIsDummy(false);

        return detail;
    }

    private AppeirInspectionDetail createDummyInspectionDetail(Integer inspectionId, Integer eirId) {
        AppeirInspectionDetail detail = new AppeirInspectionDetail();

        detail.setDamageLocation("0000");
        detail.setDamageType("16");
        detail.setDescription("");
        detail.setTpIndicator(0);
        detail.setPanelIndicatorCode("D");
        detail.setIsDamage(false);
        detail.setPanelImageFilename("D_sketch_DX24_1");
        detail.setMissingSpare(0);
        detail.setDamageCarrierCode(4);
        detail.setInspectionDetailId(eirId);
        detail.setInspectionId(inspectionId);
        detail.setIsDummy(true);

        return detail;
    }

    private List<AppeirInspectionPicture> createInspectionPictures(Integer estimateEmrDetailId) {
        List<AppeirInspectionPicture> pictures = new ArrayList<>();

        // Get photos for this damage detail
        List<EstimateEmrDetailPhoto> photos = estimateEmrDetailPhotoRepository.findByEstimateEmrDetailId(estimateEmrDetailId);

        int sequenceNumber = 1;
        for (EstimateEmrDetailPhoto photo : photos) {
            if (sequenceNumber > 3) break; // Maximum 3 photos per damage

            AppeirInspectionPicture picture = new AppeirInspectionPicture();
            picture.setInspectionDetailId(estimateEmrDetailId);
            picture.setName("imagen_" + photo.getId());

            if (photo.getAttachment() != null) {
                picture.setExtension(photo.getAttachment().getFormat() != null ? photo.getAttachment().getFormat() : "jpg");
                picture.setUrl(photo.getAttachment().getUrl() != null ? photo.getAttachment().getUrl() : DEFAULT_PHOTO_URL);
            } else {
                picture.setExtension("jpg");
                picture.setUrl(DEFAULT_PHOTO_URL);
            }

            picture.setSequenceNumber(sequenceNumber);
            pictures.add(picture);
            sequenceNumber++;
        }

        // If no photos found, add default photo
        if (pictures.isEmpty()) {
            AppeirInspectionPicture defaultPicture = new AppeirInspectionPicture();
            defaultPicture.setInspectionDetailId(estimateEmrDetailId);
            defaultPicture.setName("imagen_" + estimateEmrDetailId + "_1");
            defaultPicture.setExtension("jpg");
            defaultPicture.setUrl(DEFAULT_PHOTO_URL);
            defaultPicture.setSequenceNumber(1);
            pictures.add(defaultPicture);
        }

        return pictures;
    }

    private AppeirInspectionPicture createDummyInspectionPicture(Integer inspectionDetailId) {
        AppeirInspectionPicture picture = new AppeirInspectionPicture();
        picture.setInspectionDetailId(inspectionDetailId);
        picture.setName("imagen_" + inspectionDetailId + "_1");
        picture.setExtension("jpg");
        picture.setUrl(DEFAULT_PHOTO_URL);
        picture.setSequenceNumber(1);
        return picture;
    }

    private String determineEquipmentSubType(String familyDescription) {
        if (familyDescription == null) return "";

        String family = familyDescription.toUpperCase();
        if (family.contains("DRY CARGO") || family.contains("DRY")) {
            return "DRY";
        } else if (family.contains("TANK TAINER") || family.contains("TANK")) {
            return "TANK";
        } else if (family.contains("FLAT RACK") || family.contains("FLAT")) {
            return "FLAT";
        } else if (family.contains("REEFER") || family.contains("REEF")) {
            return "REEF";
        }
        return "";
    }

    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_FORMAT) : "";
    }

    private void updateActivityDates(List<EirSendAppeir> pendingTransmissions) {
        for (EirSendAppeir eirSendAppeir : pendingTransmissions) {
            repository.updateActivityDate(eirSendAppeir.getId(), eirSendAppeir.getActivityDate());
        }
    }
}