package com.maersk.sd1.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for inspection results
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InspectionResult {
    
    private Boolean structureInspectionCompleted;
    private Boolean structureDamaged;
    private Boolean machineryInspectionCompleted;
    private Boolean machineryDamaged;
    private Integer estimatedStructureId;
    private Integer estimatedMachineryId;
    private String scopeInspection;
    private Boolean isDamagedContainer;
}
