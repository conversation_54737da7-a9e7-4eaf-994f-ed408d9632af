package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EirActivityZone;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EirActivityZoneRepository extends JpaRepository<EirActivityZone, Integer> {

    @Query("SELECT eaz FROM EirActivityZone eaz " +
            "WHERE eaz.eir.id = :eirId " +
            "AND eaz.catZoneActivity.id = :activityId " +
            "AND eaz.active = true")
    Optional<EirActivityZone> findByEirIdAndActivityId(@Param("eirId") Integer eirId,
                                                       @Param("activityId") Integer activityId);


    @Query("SELECT eaz FROM EirActivityZone eaz " +
            "JOIN eaz.catZoneActivity cat " +
            "WHERE eaz.eir.id = :eirId " +
            "AND cat.alias = :activityAlias " +
            "AND eaz.active = true")
    Optional<EirActivityZone> findByEirIdAndActivityAlias(@Param("eirId") Integer eirId,
                                                          @Param("activityAlias") String activityAlias);
}
