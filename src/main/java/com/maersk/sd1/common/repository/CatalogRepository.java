package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Catalog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CatalogRepository extends JpaRepository<Catalog, Integer> {
    Optional<Catalog> findByAlias(String alias);

    @Query("SELECT c.id FROM Catalog c WHERE c.alias = :alias")
    Optional<Integer> findIdByAlias(@Param("alias") String alias);
}
