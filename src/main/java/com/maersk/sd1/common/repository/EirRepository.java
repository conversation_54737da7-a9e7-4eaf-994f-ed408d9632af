package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Eir;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EirRepository extends JpaRepository<Eir, Integer> {

    @Query("SELECT e FROM Eir e " +
            "LEFT JOIN FETCH e.subBusinessUnit " +
            "LEFT JOIN FETCH e.catMovement " +
            "LEFT JOIN FETCH e.vesselProgrammingDetail vpd " +
            "LEFT JOIN FETCH vpd.shippingLine " +
            "LEFT JOIN FETCH e.container " +
            "WHERE e.id = :eirId")
    Optional<Eir> findByIdWithDetails(@Param("eirId") Integer eirId);

    /**
     * Find EIR with container type information
     */
    @Query("SELECT e FROM Eir e " +
            "LEFT JOIN FETCH e.container c " +
            "LEFT JOIN FETCH c.catContainerType " +
            "WHERE e.id = :eirId")
    Optional<Eir> findByIdWithContainerType(@Param("eirId") Integer eirId);
}
