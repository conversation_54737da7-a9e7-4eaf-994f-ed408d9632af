package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EstimateEmr;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EstimateEmrRepository extends JpaRepository<EstimateEmr, Integer> {

    @Query("SELECT e FROM EstimateEmr e " +
            "JOIN e.catEstimateType cat " +
            "WHERE e.eir.id = :eirId " +
            "AND cat.alias = :estimateTypeAlias " +
            "AND e.active = true " +
            "ORDER BY e.estimateDateInspection DESC")
    Optional<EstimateEmr> findLatestByEirIdAndEstimateTypeAlias(@Param("eirId") Integer eirId,
                                                                @Param("estimateTypeAlias") String estimateTypeAlias);

    @Query("""
        SELECT e FROM EstimateEmr e
        WHERE e.eir.id = :eirId
        AND e.active = true
        """)
    List<EstimateEmr> findByEirId(@Param("eirId") Integer eirId);
}
