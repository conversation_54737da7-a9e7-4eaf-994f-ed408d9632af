package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EstimateEmr;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EstimateEmrRepository extends JpaRepository<EstimateEmr, Integer> {

    /**
     * Find the latest estimate for an EIR by estimate type
     */
    @Query("SELECT e FROM EstimateEmr e " +
           "WHERE e.eir.id = :eirId " +
           "AND e.catEstimateType.id = :estimateTypeId " +
           "AND e.active = true " +
           "ORDER BY e.estimateDateInspection DESC")
    Optional<EstimateEmr> findLatestByEirIdAndEstimateType(@Param("eirId") Integer eirId, 
                                                           @Param("estimateTypeId") Integer estimateTypeId);

    /**
     * Find the latest estimate for an EIR by estimate type alias
     */
    @Query("SELECT e FROM EstimateEmr e " +
           "JOIN e.catEstimateType cat " +
           "WHERE e.eir.id = :eirId " +
           "AND cat.alias = :estimateTypeAlias " +
           "AND e.active = true " +
           "ORDER BY e.estimateDateInspection DESC")
    Optional<EstimateEmr> findLatestByEirIdAndEstimateTypeAlias(@Param("eirId") Integer eirId, 
                                                                @Param("estimateTypeAlias") String estimateTypeAlias);
}
