package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Container;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ContainerRepository extends JpaRepository<Container, Integer> {

    /**
     * Find container with type information
     */
    @Query("SELECT c FROM Container c " +
           "LEFT JOIN FETCH c.catContainerType " +
           "WHERE c.id = :containerId")
    Optional<Container> findByIdWithType(@Param("containerId") Integer containerId);
}
