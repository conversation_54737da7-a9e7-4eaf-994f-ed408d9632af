package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EirSendAppeir;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EirSendAppeirRepository extends JpaRepository<EirSendAppeir, Integer> {

    @Modifying
    @Query("UPDATE EirSendAppeir e SET " +
           "e.flagSend = :flagSend, " +
           "e.statusCode = :statusCode, " +
           "e.resultMessage = :resultMessage, " +
           "e.eirReferenceAppeir = :inspectionId, " +
           "e.modificationDate = CURRENT_TIMESTAMP, " +
           "e.appeirTrace = CASE WHEN LENGTH(COALESCE(e.appeirTrace, '') + '->sent') > 50 " +
           "THEN SUBSTRING(COALESCE(e.appeirTrace, '') + '->sent', 1, 50) " +
           "ELSE COALESCE(e.appeirTrace, '') + '->sent' END " +
           "WHERE e.id = :eirSendAppeirId")
    void updateSendAppEirStatus(@Param("eirSendAppeirId") Integer eirSendAppeirId,
                               @Param("flagSend") Character flagSend,
                               @Param("statusCode") Integer statusCode,
                               @Param("resultMessage") String resultMessage,
                               @Param("inspectionId") Integer inspectionId);

    /**
     * Find existing EirSendAppeir records by EIR ID
     */
    @Query("SELECT e FROM EirSendAppeir e WHERE e.eir.id = :eirId")
    List<EirSendAppeir> findByEirId(@Param("eirId") Integer eirId);

    /**
     * Find active EirSendAppeir records by EIR ID
     */
    @Query("SELECT e FROM EirSendAppeir e WHERE e.eir.id = :eirId AND e.active = :active")
    List<EirSendAppeir> findByEirIdAndActive(@Param("eirId") Integer eirId, @Param("active") Boolean active);

    /**
     * Find active and not sent EirSendAppeir record by EIR ID
     */
    @Query("SELECT e FROM EirSendAppeir e WHERE e.eir.id = :eirId AND e.flagSend = '0' AND e.active = true")
    Optional<EirSendAppeir> findActiveNotSentByEirId(@Param("eirId") Integer eirId);

    @Query("SELECT COUNT(e) > 0 FROM EirSendAppeir e WHERE e.eir.id = :eirId AND e.flagSend = '1' AND e.active = true")
    boolean existsSentAndActiveByEirId(@Param("eirId") Integer eirId);

    @Query("SELECT COUNT(e) > 0 FROM EirSendAppeir e WHERE e.eir.id = :eirId AND e.flagSend = '1' " +
           "AND e.scopeInspection = :scopeInspection AND e.active = true")
    boolean existsSentWithScopeInspection(@Param("eirId") Integer eirId, @Param("scopeInspection") String scopeInspection);

    @Query("SELECT COUNT(e) > 0 FROM EirSendAppeir e WHERE e.eir.id = :eirId " +
           "AND COALESCE(e.scopeInspection, '') = COALESCE(:scopeInspection, '') " +
           "AND COALESCE(e.estimatedEstructureId, 0) = COALESCE(:estimatedStructureId, 0) " +
           "AND COALESCE(e.estimatedMachineryId, 0) = COALESCE(:estimatedMachineryId, 0) " +
           "AND e.active = true")
    boolean existsWithSameCriteria(@Param("eirId") Integer eirId,
                                   @Param("scopeInspection") String scopeInspection,
                                   @Param("estimatedStructureId") Integer estimatedStructureId,
                                   @Param("estimatedMachineryId") Integer estimatedMachineryId);

    @Modifying
    @Query("UPDATE EirSendAppeir e SET e.active = false, " +
           "e.comment = CASE WHEN LENGTH(COALESCE(e.comment, '') + ' | ' + :additionalComment) > 200 " +
           "THEN SUBSTRING(COALESCE(e.comment, '') + ' | ' + :additionalComment, 1, 200) " +
           "ELSE COALESCE(e.comment, '') + CASE WHEN COALESCE(e.comment, '') = '' THEN '' ELSE ' | ' END + :additionalComment END, " +
           "e.modificationDate = CURRENT_TIMESTAMP, " +
           "e.appeirTrace = CASE WHEN LENGTH(COALESCE(e.appeirTrace, '') + :traceValue) > 50 " +
           "THEN SUBSTRING(COALESCE(e.appeirTrace, '') + :traceValue, 1, 50) " +
           "ELSE COALESCE(e.appeirTrace, '') + :traceValue END " +
           "WHERE e.eir.id = :eirId AND e.active = true AND e.flagSend <> '1'")
    void deactivateByEirIdWithComment(@Param("eirId") Integer eirId,
                                      @Param("additionalComment") String additionalComment,
                                      @Param("traceValue") String traceValue);

    @Modifying
    @Query("UPDATE EirSendAppeir e SET " +
           "e.scopeInspection = :scopeInspection, " +
           "e.isDamagedContainer = :isDamagedContainer, " +
           "e.estimatedEstructureId = :estimatedStructureId, " +
           "e.estimatedMachineryId = :estimatedMachineryId, " +
           "e.modificationDate = CURRENT_TIMESTAMP " +
           "WHERE e.id = :eirSendAppeirId")
    void updateInspectionDetails(@Param("eirSendAppeirId") Integer eirSendAppeirId,
                                @Param("scopeInspection") String scopeInspection,
                                @Param("isDamagedContainer") Boolean isDamagedContainer,
                                @Param("estimatedStructureId") Integer estimatedStructureId,
                                @Param("estimatedMachineryId") Integer estimatedMachineryId);

    @Query("""
        SELECT esa FROM EirSendAppeir esa
        INNER JOIN esa.eir e
        WHERE e.subBusinessUnit.id = :subBusinessUnitId
        AND e.shippingLine.id = :shippingLineId
        AND esa.flagSend = '0'
        AND esa.isNewInsert = false
        AND e.truckDepartureDate IS NOT NULL
        AND FUNCTION('DATEDIFF', 'day', e.truckArrivalDate, CURRENT_TIMESTAMP) <= 20
        AND esa.active = true
        """)
    List<EirSendAppeir> findPendingWithoutTransmission(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("shippingLineId") Integer shippingLineId
    );
}
