package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.DepotCredentialAppeir;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface DepotCredentialAppeirRepository extends JpaRepository<DepotCredentialAppeir, Integer> {

    @Query("SELECT d FROM DepotCredentialAppeir d WHERE " +
           "d.subBusinessUnit.id = :subBusinessUnitId " +
           "AND d.shippingLineId = :shippingLineId " +
           "AND d.sendGateIn = true " +
           "AND d.active = true")
    Optional<DepotCredentialAppeir> findBySubBusinessUnitAndShippingLineForGateIn(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("shippingLineId") Integer shippingLineId);

    @Query("SELECT d FROM DepotCredentialAppeir d WHERE " +
            "d.subBusinessUnit.id = :subBusinessUnitId " +
            "AND d.shippingLineId = :shippingLineId " +
            "AND d.sendGateIn = true " +
            "AND d.active = true")
    Optional<DepotCredentialAppeir> findBySubBusinessUnitAndShippingLineForGateIn(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("shippingLineId") Integer shippingLineId);
}
