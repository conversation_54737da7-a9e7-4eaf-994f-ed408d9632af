package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.DepotCredentialAppeir;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DepotCredentialAppeirRepository extends JpaRepository<DepotCredentialAppeir, Integer> {

    /**
     * Find all active depot credentials ordered by registration date
     * Equivalent to the SQL query with WHERE active = 1 ORDER BY registration_date
     */
    List<DepotCredentialAppeir> findByActiveOrderByRegistrationDate(Boolean active);

    /**
     * Alternative method using @Query annotation for more explicit SQL control
     */
    @Query("SELECT d FROM DepotCredentialAppeir d WHERE d.active = true ORDER BY d.registrationDate")
    List<DepotCredentialAppeir> findActiveDepotCredentialsOrderedByRegistrationDate();

    /**
     * Native SQL query method that exactly matches your SQL
     */
    @Query(value = "SELECT CRE.depot_credential_appeir_id, " +
                   "CRE.sub_business_unit_id, " +
                   "CRE.url, " +
                   "CRE.client_id, " +
                   "CRE.client_secret, " +
                   "CRE.shop_email_copy, " +
                   "CRE.active, " +
                   "CRE.user_registration_id, " +
                   "CRE.registration_date, " +
                   "CRE.user_modification_id, " +
                   "CRE.modification_date, " +
                   "CRE.shipping_line_id, " +
                   "CRE.business_unit_id, " +
                   "CRE.send_gate_in, " +
                   "CRE.send_gate_out " +
                   "FROM sde.depot_credential_appeir AS CRE WITH (NOLOCK) " +
                   "WHERE CRE.active = 1 " +
                   "ORDER BY CRE.registration_date", nativeQuery = true)
    List<Object[]> findActiveDepotCredentialsNative();

    /**
     * Find depot credential for specific sub business unit and shipping line with gate in enabled
     */
    @Query("SELECT d FROM DepotCredentialAppeir d WHERE " +
           "d.subBusinessUnit.id = :subBusinessUnitId " +
           "AND d.shippingLineId = :shippingLineId " +
           "AND d.sendGateIn = true " +
           "AND d.active = true")
    Optional<DepotCredentialAppeir> findBySubBusinessUnitAndShippingLineForGateIn(
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("shippingLineId") Integer shippingLineId);
}
