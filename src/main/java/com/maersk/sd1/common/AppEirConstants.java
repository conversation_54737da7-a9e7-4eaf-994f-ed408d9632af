package com.maersk.sd1.common;

/**
 * Constants for AppEIR gate operations
 */
public final class AppEirConstants {
    
    // Movement type catalog aliases
    public static final String CATALOG_GATE_OUT_ALIAS = "43081";
    public static final String CATALOG_GATE_IN_ALIAS = "43080";
    
    // Estimate type catalog aliases
    public static final String CATALOG_ESTIMATE_TYPE_STRUCTURE_ALIAS = "47490";
    public static final String CATALOG_ESTIMATE_TYPE_MACHINERY_ALIAS = "47491";
    
    // Activity zone catalog aliases
    public static final String CATALOG_ACTIVITY_INSPECTION_STRUCTURE_ALIAS = "cat_43161_box_inspection";
    public static final String CATALOG_ACTIVITY_INSPECTION_MACHINERY_ALIAS = "cat_43161_pti";
    
    // Scope inspection values
    public static final String SCOPE_INSPECTION_NONE = "N";
    public static final String SCOPE_INSPECTION_STRUCTURE_ONLY = "E";
    public static final String SCOPE_INSPECTION_MACHINERY_ONLY = "M";
    public static final String SCOPE_INSPECTION_BOTH = "A";
    
    // Container type codes
    public static final String CONTAINER_TYPE_DRY_CODE = "0";
    
    // Flag send values
    public static final Character FLAG_SEND_NOT_SENT = '0';
    public static final Character FLAG_SEND_SENT = '1';
    
    // Days limit for processing
    public static final int DAYS_LIMIT_FOR_PROCESSING = 20;
    
    // Comment prefixes
    public static final String COMMENT_ADAPTATION = "[adaptation]";
    public static final String COMMENT_ADD1 = "[add1]";
    public static final String COMMENT_ADD2 = "[add2]";
    public static final String COMMENT_TRIGGER = "trigger";
    
    // AppEIR trace values
    public static final String TRACE_NEW2 = "new2";
    public static final String TRACE_NEW3 = "new3";
    public static final String TRACE_DESACTIVE1 = "->desactive1";
    public static final String TRACE_DESACTIVE2 = "->desactive2";
    
    private AppEirConstants() {
        // Utility class
    }
}
