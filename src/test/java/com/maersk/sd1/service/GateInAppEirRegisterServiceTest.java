package com.maersk.sd1.service;

import com.maersk.sd1.dto.AppEirConstants;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GateInAppEirRegisterServiceTest {

    @Mock
    private EirRepository eirRepository;

    @Mock
    private EirSendAppeirRepository eirSendAppeirRepository;

    @Mock
    private DepotCredentialAppeirRepository depotCredentialAppeirRepository;

    @Mock
    private EirActivityZoneRepository eirActivityZoneRepository;

    @Mock
    private EstimateEmrRepository estimateEmrRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private ContainerRepository containerRepository;

    @InjectMocks
    private GateInAppEirRegisterService service;

    private Eir mockEir;
    private DepotCredentialAppeir mockDepotCredential;
    private Container mockContainer;
    private Catalog mockContainerType;
    private Catalog mockCatMovement;

    @BeforeEach
    void setUp() {
        BusinessUnit mockSubBusinessUnit = new BusinessUnit();
        mockSubBusinessUnit.setId(1);

        ShippingLine mockShippingLine = new ShippingLine();
        mockShippingLine.setId(1);

        mockCatMovement = new Catalog();
        mockCatMovement.setId(1);

        mockContainerType = new Catalog();
        mockContainerType.setCode("1");

        mockContainer = new Container();
        mockContainer.setId(1);
        mockContainer.setCatContainerType(mockContainerType);

        mockEir = new Eir();
        mockEir.setId(1);
        mockEir.setActive(true);
        mockEir.setSubBusinessUnit(mockSubBusinessUnit);
        mockEir.setShippingLine(mockShippingLine);
        mockEir.setCatMovement(mockCatMovement);
        mockEir.setContainer(mockContainer);
        mockEir.setTruckArrivalDate(LocalDateTime.now().minusDays(5));

        mockDepotCredential = new DepotCredentialAppeir();
        mockDepotCredential.setId(1);
        mockDepotCredential.setSubBusinessUnit(mockSubBusinessUnit);
    }

    @Test
    void processGateInAppEirRegister_WhenEirNotFound_ShouldLogWarningAndReturn() {
        Integer eirId = 999;
        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1));
        when(eirRepository.findByIdWithDetails(eirId)).thenReturn(Optional.empty());

        service.processGateInAppEirRegister(eirId, "test");

        verify(eirRepository).findByIdWithDetails(eirId);
        verifyNoInteractions(eirSendAppeirRepository);
    }

    @Test
    void processGateInAppEirRegister_WhenEirIsInactive_ShouldHandleInactiveEir() {
        Integer eirId = 1;
        mockEir.setActive(false);

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1));
        when(eirRepository.findByIdWithDetails(eirId)).thenReturn(Optional.of(mockEir));
        when(eirSendAppeirRepository.findByEirIdAndActive(eirId, true))
                .thenReturn(List.of(new EirSendAppeir()));

        service.processGateInAppEirRegister(eirId, "test");

        verify(eirSendAppeirRepository).deactivateByEirIdWithComment(
                eirId, "eir deleted", AppEirConstants.TRACE_DESACTIVE1);
    }

    @Test
    void processGateInAppEirRegister_WhenNotGateInMovement_ShouldSkip() {
        Integer eirId = 1;
        mockCatMovement.setId(2);

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1));
        when(eirRepository.findByIdWithDetails(eirId)).thenReturn(Optional.of(mockEir));

        service.processGateInAppEirRegister(eirId, "test");

        verify(eirRepository).findByIdWithDetails(eirId);
        verifyNoMoreInteractions(eirSendAppeirRepository);
    }

    @Test
    void processGateInAppEirRegister_WhenDepotCredentialNotFound_ShouldHandleMissingCredential() {
        Integer eirId = 1;

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1));
        when(eirRepository.findByIdWithDetails(eirId)).thenReturn(Optional.of(mockEir));
        when(depotCredentialAppeirRepository.findBySubBusinessUnitAndShippingLineForGateIn(1, 1))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findByEirIdAndActive(eirId, true))
                .thenReturn(List.of(new EirSendAppeir()));

        service.processGateInAppEirRegister(eirId, "test");

        verify(eirSendAppeirRepository).deactivateByEirIdWithComment(
                eirId,
                "shipping line not configured for transmissions",
                AppEirConstants.TRACE_DESACTIVE2
        );
    }

    @Test
    void processGateInAppEirRegister_WhenOutsideTimeLimit_ShouldSkip() {
        Integer eirId = 1;
        mockEir.setTruckArrivalDate(LocalDateTime.now().minusDays(25)); // Outside 20-day limit

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1));
        when(eirRepository.findByIdWithDetails(eirId)).thenReturn(Optional.of(mockEir));
        when(depotCredentialAppeirRepository.findBySubBusinessUnitAndShippingLineForGateIn(1, 1))
                .thenReturn(Optional.of(mockDepotCredential));

        service.processGateInAppEirRegister(eirId, "test");

        verifyNoInteractions(eirSendAppeirRepository);
    }

    @Test
    void processGateInAppEirRegister_WhenNoExistingRecord_ShouldCreateNew() {
        Integer eirId = 1;

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1));
        when(eirRepository.findByIdWithDetails(eirId)).thenReturn(Optional.of(mockEir));
        when(depotCredentialAppeirRepository.findBySubBusinessUnitAndShippingLineForGateIn(1, 1))
                .thenReturn(Optional.of(mockDepotCredential));
        when(containerRepository.findByIdWithType(1)).thenReturn(Optional.of(mockContainer));
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_STRUCTURE_ALIAS))
                .thenReturn(Optional.empty());
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_MACHINERY_ALIAS))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findActiveNotSentByEirId(eirId))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findByEirIdAndActive(eirId, true))
                .thenReturn(Collections.emptyList());

        service.processGateInAppEirRegister(eirId, "test");

        verify(eirSendAppeirRepository).save(any(EirSendAppeir.class));
    }

    @Test
    void processGateInAppEirRegister_WhenExistingRecordExists_ShouldUpdate() {
        Integer eirId = 1;
        EirSendAppeir existingRecord = new EirSendAppeir();
        existingRecord.setId(1);

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1));
        when(eirRepository.findByIdWithDetails(eirId)).thenReturn(Optional.of(mockEir));
        when(depotCredentialAppeirRepository.findBySubBusinessUnitAndShippingLineForGateIn(1, 1))
                .thenReturn(Optional.of(mockDepotCredential));
        when(containerRepository.findByIdWithType(1)).thenReturn(Optional.of(mockContainer));
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_STRUCTURE_ALIAS))
                .thenReturn(Optional.empty());
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_MACHINERY_ALIAS))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findActiveNotSentByEirId(eirId))
                .thenReturn(Optional.of(existingRecord));

        service.processGateInAppEirRegister(eirId, "test");

        verify(eirSendAppeirRepository).updateInspectionDetails(
                eq(1), eq(AppEirConstants.SCOPE_INSPECTION_NONE), eq(false), isNull(), isNull());
    }

    @Test
    void processGateInAppEirRegister_WhenStructureInspectionCompleted_ShouldSetCorrectScope() {
        Integer eirId = 1;
        EirActivityZone structureActivity = new EirActivityZone();
        structureActivity.setConcluded(true);
        structureActivity.setStructureDamagedResult(false);

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1));
        when(eirRepository.findByIdWithDetails(eirId)).thenReturn(Optional.of(mockEir));
        when(depotCredentialAppeirRepository.findBySubBusinessUnitAndShippingLineForGateIn(1, 1))
                .thenReturn(Optional.of(mockDepotCredential));
        when(containerRepository.findByIdWithType(1)).thenReturn(Optional.of(mockContainer));
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_STRUCTURE_ALIAS))
                .thenReturn(Optional.of(structureActivity));
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_MACHINERY_ALIAS))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findActiveNotSentByEirId(eirId))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findByEirIdAndActive(eirId, true))
                .thenReturn(Collections.emptyList());

        service.processGateInAppEirRegister(eirId, "test");

        verify(eirSendAppeirRepository).save(any(EirSendAppeir.class));
    }

    @Test
    void processGateInAppEirRegister_WhenStructureDamaged_ShouldIncludeEstimate() {
        Integer eirId = 1;
        EirActivityZone structureActivity = new EirActivityZone();
        structureActivity.setConcluded(true);
        structureActivity.setStructureDamagedResult(true);

        EstimateEmr estimate = new EstimateEmr();
        estimate.setId(10);

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1));
        when(eirRepository.findByIdWithDetails(eirId)).thenReturn(Optional.of(mockEir));
        when(depotCredentialAppeirRepository.findBySubBusinessUnitAndShippingLineForGateIn(1, 1))
                .thenReturn(Optional.of(mockDepotCredential));
        when(containerRepository.findByIdWithType(1)).thenReturn(Optional.of(mockContainer));
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_STRUCTURE_ALIAS))
                .thenReturn(Optional.of(structureActivity));
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_MACHINERY_ALIAS))
                .thenReturn(Optional.empty());
        when(estimateEmrRepository.findLatestByEirIdAndEstimateTypeAlias(
                eirId, AppEirConstants.CATALOG_ESTIMATE_TYPE_STRUCTURE_ALIAS))
                .thenReturn(Optional.of(estimate));
        when(eirSendAppeirRepository.findActiveNotSentByEirId(eirId))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findByEirIdAndActive(eirId, true))
                .thenReturn(Collections.emptyList());

        service.processGateInAppEirRegister(eirId, "test");

        verify(eirSendAppeirRepository).save(any(EirSendAppeir.class));
        verify(estimateEmrRepository).findLatestByEirIdAndEstimateTypeAlias(
                eirId, AppEirConstants.CATALOG_ESTIMATE_TYPE_STRUCTURE_ALIAS);
    }

    @Test
    void processGateInAppEirRegister_WhenReeferWithMachineryInspection_ShouldHandleMachinery() {
        Integer eirId = 1;
        EirActivityZone machineryActivity = new EirActivityZone();
        machineryActivity.setConcluded(true);
        machineryActivity.setMachineryDamagedResult(false);

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1));
        when(eirRepository.findByIdWithDetails(eirId)).thenReturn(Optional.of(mockEir));
        when(depotCredentialAppeirRepository.findBySubBusinessUnitAndShippingLineForGateIn(1, 1))
                .thenReturn(Optional.of(mockDepotCredential));
        when(containerRepository.findByIdWithType(1)).thenReturn(Optional.of(mockContainer));
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_STRUCTURE_ALIAS))
                .thenReturn(Optional.empty());
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_MACHINERY_ALIAS))
                .thenReturn(Optional.of(machineryActivity));
        when(eirSendAppeirRepository.existsSentWithScopeInspection(
                eirId, AppEirConstants.SCOPE_INSPECTION_MACHINERY_ONLY))
                .thenReturn(false);
        when(eirSendAppeirRepository.existsSentWithScopeInspection(
                eirId, AppEirConstants.SCOPE_INSPECTION_STRUCTURE_ONLY))
                .thenReturn(false);
        when(eirSendAppeirRepository.findActiveNotSentByEirId(eirId))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findByEirIdAndActive(eirId, true))
                .thenReturn(Collections.emptyList());

        service.processGateInAppEirRegister(eirId, "test");

        verify(eirSendAppeirRepository).save(any(EirSendAppeir.class));
    }

    @Test
    void processGateInAppEirRegister_WhenAdditionalRecordNeeded_ShouldCreateAdditionalRecord() {
        Integer eirId = 1;
        mockEir.setTruckDepartureDate(LocalDateTime.now());

        EirActivityZone structureActivity = new EirActivityZone();
        structureActivity.setConcluded(true);
        structureActivity.setStructureDamagedResult(true);

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1));
        when(eirRepository.findByIdWithDetails(eirId)).thenReturn(Optional.of(mockEir));
        when(depotCredentialAppeirRepository.findBySubBusinessUnitAndShippingLineForGateIn(1, 1))
                .thenReturn(Optional.of(mockDepotCredential));
        when(containerRepository.findByIdWithType(1)).thenReturn(Optional.of(mockContainer));
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_STRUCTURE_ALIAS))
                .thenReturn(Optional.of(structureActivity));
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_MACHINERY_ALIAS))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findActiveNotSentByEirId(eirId))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findByEirIdAndActive(eirId, true))
                .thenReturn(Collections.emptyList());
        when(eirSendAppeirRepository.existsSentAndActiveByEirId(eirId))
                .thenReturn(true);
        when(eirSendAppeirRepository.existsWithSameCriteria(anyInt(), anyString(), any(), any()))
                .thenReturn(false);

        service.processGateInAppEirRegister(eirId, "trigger");

        verify(eirSendAppeirRepository, times(2)).save(any(EirSendAppeir.class));
    }

    @Test
    void processGateInAppEirRegister_WhenCatalogNotFound_ShouldThrowException() {
        Integer eirId = 1;
        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.empty());

        assertThrows(IllegalStateException.class, () ->
                service.processGateInAppEirRegister(eirId, "test"));
    }

    @Test
    void processGateInAppEirRegister_WhenDryContainer_ShouldNotProcessMachinery() {
        Integer eirId = 1;
        mockContainerType.setCode(AppEirConstants.CONTAINER_TYPE_DRY_CODE);

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1));
        when(eirRepository.findByIdWithDetails(eirId)).thenReturn(Optional.of(mockEir));
        when(depotCredentialAppeirRepository.findBySubBusinessUnitAndShippingLineForGateIn(1, 1))
                .thenReturn(Optional.of(mockDepotCredential));
        when(containerRepository.findByIdWithType(1)).thenReturn(Optional.of(mockContainer));
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_STRUCTURE_ALIAS))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findActiveNotSentByEirId(eirId))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findByEirIdAndActive(eirId, true))
                .thenReturn(Collections.emptyList());

        service.processGateInAppEirRegister(eirId, "test");

        verify(eirActivityZoneRepository, never()).findByEirIdAndActivityAlias(
                eirId, AppEirConstants.CATALOG_ACTIVITY_INSPECTION_MACHINERY_ALIAS);
    }
}