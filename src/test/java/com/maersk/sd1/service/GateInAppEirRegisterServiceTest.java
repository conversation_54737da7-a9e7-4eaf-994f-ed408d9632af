package com.maersk.sd1.service;

import com.maersk.sd1.common.AppEirConstants;
import com.maersk.sd1.common.dto.GateInAppEirRequest;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GateInAppEirRegisterServiceTest {

    @Mock
    private EirRepository eirRepository;
    @Mock
    private EirSendAppeirRepository eirSendAppeirRepository;
    @Mock
    private DepotCredentialAppeirRepository depotCredentialAppeirRepository;
    @Mock
    private EirActivityZoneRepository eirActivityZoneRepository;
    @Mock
    private EstimateEmrRepository estimateEmrRepository;
    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private ContainerRepository containerRepository;

    @InjectMocks
    private GateInAppEirRegisterService service;

    private Eir mockEir;
    private DepotCredentialAppeir mockDepotCredential;
    private Container mockContainer;

    @BeforeEach
    void setUp() {
        // Setup mock EIR
        mockEir = Eir.builder()
                .id(1)
                .active(true)
                .truckArrivalDate(LocalDateTime.now().minusDays(1))
                .subBusinessUnit(BusinessUnit.builder().id(100).build())
                .catMovement(new Catalog(1001)) // Gate In movement
                .container(Container.builder().id(200).build())
                .vesselProgrammingDetail(VesselProgrammingDetail.builder()
                        .shippingLine(ShippingLine.builder().id(300).build())
                        .build())
                .build();

        // Setup mock depot credential
        mockDepotCredential = DepotCredentialAppeir.builder()
                .id(400)
                .subBusinessUnit(BusinessUnit.builder().id(100).build())
                .shippingLineId(300)
                .sendGateIn(true)
                .active(true)
                .build();

        // Setup mock container
        mockContainer = Container.builder()
                .id(200)
                .catContainerType(Catalog.builder().code("0").build()) // Dry container
                .build();
    }

    @Test
    void processGateInAppEirRegister_WithValidEir_ShouldProcessSuccessfully() {
        // Arrange
        GateInAppEirRequest request = GateInAppEirRequest.builder()
                .eirId(1)
                .comment("test comment")
                .build();

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1001));
        when(eirRepository.findByIdWithDetails(1))
                .thenReturn(Optional.of(mockEir));
        when(depotCredentialAppeirRepository.findBySubBusinessUnitAndShippingLineForGateIn(100, 300))
                .thenReturn(Optional.of(mockDepotCredential));
        when(containerRepository.findByIdWithType(200))
                .thenReturn(Optional.of(mockContainer));
        when(eirSendAppeirRepository.findActiveNotSentByEirId(1))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findByEirIdAndActive(1, true))
                .thenReturn(Collections.emptyList());
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(eq(1), anyString()))
                .thenReturn(Optional.empty());

        // Act
        service.processGateInAppEirRegister(request);

        // Assert
        verify(eirSendAppeirRepository, times(1)).save(any(EirSendAppeir.class));
    }

    @Test
    void processGateInAppEirRegister_WithInactiveEir_ShouldDeactivateRecords() {
        // Arrange
        GateInAppEirRequest request = GateInAppEirRequest.builder()
                .eirId(1)
                .comment("test comment")
                .build();

        Eir inactiveEir = Eir.builder()
                .id(1)
                .active(false)
                .build();

        when(eirRepository.findByIdWithDetails(1))
                .thenReturn(Optional.of(inactiveEir));
        when(eirSendAppeirRepository.findByEirIdAndActive(1, true))
                .thenReturn(Collections.singletonList(new EirSendAppeir()));

        // Act
        service.processGateInAppEirRegister(request);

        // Assert
        verify(eirSendAppeirRepository, times(1))
                .deactivateByEirIdWithComment(eq(1), eq("eir deleted"), eq(AppEirConstants.TRACE_DESACTIVE1));
    }

    @Test
    void processGateInAppEirRegister_WithNonExistentEir_ShouldReturnEarly() {
        // Arrange
        GateInAppEirRequest request = GateInAppEirRequest.builder()
                .eirId(999)
                .comment("test comment")
                .build();

        when(eirRepository.findByIdWithDetails(999))
                .thenReturn(Optional.empty());

        // Act
        service.processGateInAppEirRegister(request);

        // Assert
        verify(eirSendAppeirRepository, never()).save(any());
        verify(eirSendAppeirRepository, never()).deactivateByEirIdWithComment(anyInt(), anyString(), anyString());
    }

    @Test
    void processGateInAppEirRegister_WithMissingDepotCredential_ShouldDeactivateRecords() {
        // Arrange
        GateInAppEirRequest request = GateInAppEirRequest.builder()
                .eirId(1)
                .comment("test comment")
                .build();

        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1001));
        when(eirRepository.findByIdWithDetails(1))
                .thenReturn(Optional.of(mockEir));
        when(depotCredentialAppeirRepository.findBySubBusinessUnitAndShippingLineForGateIn(100, 300))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findByEirIdAndActive(1, true))
                .thenReturn(Collections.singletonList(new EirSendAppeir()));

        // Act
        service.processGateInAppEirRegister(request);

        // Assert
        verify(eirSendAppeirRepository, times(1))
                .deactivateByEirIdWithComment(eq(1), anyString(), eq(AppEirConstants.TRACE_DESACTIVE2));
    }

    @Test
    void registerGateInAppEir_LegacyMethod_ShouldCallNewMethod() {
        // Arrange
        when(catalogRepository.findIdByAlias(AppEirConstants.CATALOG_GATE_IN_ALIAS))
                .thenReturn(Optional.of(1001));
        when(eirRepository.findByIdWithDetails(1))
                .thenReturn(Optional.of(mockEir));
        when(depotCredentialAppeirRepository.findBySubBusinessUnitAndShippingLineForGateIn(100, 300))
                .thenReturn(Optional.of(mockDepotCredential));
        when(containerRepository.findByIdWithType(200))
                .thenReturn(Optional.of(mockContainer));
        when(eirSendAppeirRepository.findActiveNotSentByEirId(1))
                .thenReturn(Optional.empty());
        when(eirSendAppeirRepository.findByEirIdAndActive(1, true))
                .thenReturn(Collections.emptyList());
        when(eirActivityZoneRepository.findByEirIdAndActivityAlias(eq(1), anyString()))
                .thenReturn(Optional.empty());

        // Act
        service.registerGateInAppEir(1, "test comment");

        // Assert
        verify(eirSendAppeirRepository, times(1)).save(any(EirSendAppeir.class));
    }
}
